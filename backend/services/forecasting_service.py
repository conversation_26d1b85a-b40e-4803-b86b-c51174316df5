"""
Enhanced Sales Forecasting Service with Multiple Models
Provides sales predictions using Prophet, Merlion, StatsForecasting, SKForecast, SKTime, StatsModels, and ADTK for anomaly detection
"""

import pandas as pd
import numpy as np
from prophet import Prophet
from typing import Dict, List, Optional, Tuple, Any
from datetime import datetime, timed<PERSON>ta
from sqlalchemy.orm import Session
from models import Product, SalesData, ForecastData, Order
import logging
import json
import warnings

warnings.filterwarnings("ignore")

# Import forecasting libraries
try:
    from merlion.models.forecast.arima import Arima
    from merlion.models.forecast.ets import ETS
    from merlion.models.forecast.prophet import Prophet as MerlionProphet
    from merlion.utils import TimeSeries

    MERLION_AVAILABLE = True
except ImportError:
    MERLION_AVAILABLE = False

try:
    from statsforecast import StatsForecast
    from statsforecast.models import AutoARIMA, ETS as StatsETS, Naive, SeasonalNaive

    STATSFORECAST_AVAILABLE = True
except ImportError:
    STATSFORECAST_AVAILABLE = False

try:
    from skforecast.recursive import ForecasterRecursive
    from sklearn.ensemble import RandomForestRegressor
    from sklearn.linear_model import LinearRegression

    SKFORECAST_AVAILABLE = True
except ImportError:
    SKFORECAST_AVAILABLE = False

try:
    from sktime.forecasting.arima import ARIMA as SKTimeARIMA
    from sktime.forecasting.exp_smoothing import ExponentialSmoothing
    from sktime.forecasting.naive import NaiveForecaster

    SKTIME_AVAILABLE = True
except ImportError:
    SKTIME_AVAILABLE = False

try:
    import statsmodels.api as sm
    from statsmodels.tsa.arima.model import ARIMA as StatsARIMA
    from statsmodels.tsa.holtwinters import ExponentialSmoothing as StatsExpSmoothing

    STATSMODELS_AVAILABLE = True
except ImportError:
    STATSMODELS_AVAILABLE = False

try:
    from adtk.detector import ThresholdAD, QuantileAD, InterQuartileRangeAD
    from adtk.data import validate_series

    ADTK_AVAILABLE = True
except ImportError:
    ADTK_AVAILABLE = False

logger = logging.getLogger(__name__)


class SalesForecastingService:
    """Enhanced service for generating sales forecasts using multiple models"""

    def __init__(self, db: Session):
        self.db = db
        self.available_models = self._get_available_models()

    def _get_available_models(self) -> List[str]:
        """Get list of available forecasting models"""
        models = ["prophet"]  # Prophet is always available

        if MERLION_AVAILABLE:
            models.extend(["merlion_arima", "merlion_ets", "merlion_prophet"])
        if STATSFORECAST_AVAILABLE:
            models.extend(["stats_autoarima", "stats_ets", "stats_naive"])
        if SKFORECAST_AVAILABLE:
            models.extend(["skforecast_rf", "skforecast_lr"])
        if SKTIME_AVAILABLE:
            models.extend(["sktime_arima", "sktime_exp_smoothing"])
        if STATSMODELS_AVAILABLE:
            models.extend(["statsmodels_arima", "statsmodels_exp_smoothing"])

        return models

    def generate_multi_model_forecast(
        self,
        product_id: int,
        days_ahead: int = 30,
        confidence_interval: float = 0.95,
        models: Optional[List[str]] = None,
    ) -> Dict[str, Any]:
        """
        Generate forecasts using multiple models

        Args:
            product_id: ID of the product to forecast
            days_ahead: Number of days to forecast ahead
            confidence_interval: Confidence interval for predictions
            models: List of models to use (if None, uses all available)

        Returns:
            Dictionary containing forecasts from all models and anomaly detection results
        """
        try:
            # Get historical sales data
            sales_data = self._get_historical_sales_data(product_id)

            if len(sales_data) < 10:  # Need minimum data points
                return {
                    "success": False,
                    "message": "Insufficient historical data for forecasting (minimum 10 data points required)",
                    "data_points": len(sales_data),
                    "suggestion": "Try populating sales data from existing orders using the /api/products/populate-sales-data endpoint, or ensure you have sufficient order history.",
                }

            # Prepare data
            df = self._prepare_prophet_data(sales_data)

            # Use all available models if none specified
            if models is None:
                models = self.available_models

            # Generate forecasts for each model
            forecasts = {}
            for model_name in models:
                try:
                    forecast_result = self._generate_single_model_forecast(
                        df, model_name, days_ahead, confidence_interval
                    )
                    if forecast_result:
                        forecasts[model_name] = forecast_result
                except Exception as e:
                    logger.warning(f"Failed to generate forecast with {model_name}: {str(e)}")
                    continue

            # Detect anomalies in historical data
            anomalies = self._detect_anomalies(df) if ADTK_AVAILABLE else []

            # Calculate ensemble forecast (average of all models)
            ensemble_forecast = self._calculate_ensemble_forecast(forecasts, days_ahead)

            return {
                "success": True,
                "product_id": product_id,
                "forecasts": forecasts,
                "ensemble_forecast": ensemble_forecast,
                "anomalies": anomalies,
                "available_models": list(forecasts.keys()),
                "historical_data_points": len(sales_data),
                "forecast_period_days": days_ahead,
                "generated_at": datetime.now().isoformat(),
            }

        except Exception as e:
            logger.error(f"Error generating multi-model forecast: {str(e)}")
            return {
                "success": False,
                "message": f"Error generating forecast: {str(e)}",
            }

    def generate_forecast(
        self, product_id: int, days_ahead: int = 30, confidence_interval: float = 0.95
    ) -> Dict[str, Any]:
        """
        Generate sales forecast for a specific product

        Args:
            product_id: ID of the product to forecast
            days_ahead: Number of days to forecast ahead
            confidence_interval: Confidence interval for predictions (0.8, 0.95, etc.)

        Returns:
            Dictionary containing forecast data and metadata
        """
        try:
            # Get historical sales data
            sales_data = self._get_historical_sales_data(product_id)

            if len(sales_data) < 10:  # Need minimum data points
                return {
                    "success": False,
                    "message": "Insufficient historical data for forecasting (minimum 10 data points required)",
                    "data_points": len(sales_data),
                    "suggestion": "Try populating sales data from existing orders using the /api/products/populate-sales-data endpoint, or ensure you have sufficient order history.",
                }

            # Prepare data for Prophet
            df = self._prepare_prophet_data(sales_data)

            # Create and fit Prophet model
            model = Prophet(
                interval_width=confidence_interval,
                daily_seasonality=True,
                weekly_seasonality=True,
                yearly_seasonality=True if len(sales_data) > 365 else False,
            )

            model.fit(df)

            # Create future dataframe
            future = model.make_future_dataframe(periods=days_ahead)

            # Generate forecast
            forecast = model.predict(future)

            # Extract forecast data for the prediction period
            forecast_data = self._extract_forecast_data(forecast, days_ahead)

            # Save forecast to database
            self._save_forecast_to_db(product_id, forecast_data, confidence_interval)

            # Calculate accuracy metrics if we have enough historical data
            accuracy_metrics = self._calculate_accuracy_metrics(df, model) if len(df) > 30 else None

            return {
                "success": True,
                "product_id": product_id,
                "forecast_period_days": days_ahead,
                "confidence_interval": confidence_interval,
                "historical_data_points": len(sales_data),
                "forecast_data": forecast_data,
                "accuracy_metrics": accuracy_metrics,
                "generated_at": datetime.now().isoformat(),
            }

        except Exception as e:
            logger.error(f"Error generating forecast for product {product_id}: {str(e)}")
            return {"success": False, "message": f"Forecast generation failed: {str(e)}"}

    def get_product_forecast(self, product_id: int, days_ahead: int = 30) -> Optional[Dict]:
        """Get existing forecast for a product or generate new one if needed"""

        # Check if we have recent forecast data
        recent_forecast = (
            self.db.query(ForecastData)
            .filter(
                ForecastData.product_id == product_id,
                ForecastData.created_at >= datetime.now() - timedelta(hours=24),  # Refresh daily
            )
            .first()
        )

        if recent_forecast:
            # Return existing forecast
            all_forecasts = (
                self.db.query(ForecastData)
                .filter(ForecastData.product_id == product_id, ForecastData.forecast_date >= datetime.now().date())
                .order_by(ForecastData.forecast_date)
                .limit(days_ahead)
                .all()
            )

            return {
                "success": True,
                "product_id": product_id,
                "forecast_data": [
                    {
                        "date": f.forecast_date.isoformat(),
                        "predicted_sales": f.predicted_sales,
                        "lower_bound": f.lower_bound,
                        "upper_bound": f.upper_bound,
                    }
                    for f in all_forecasts
                ],
                "last_updated": recent_forecast.created_at.isoformat(),
            }
        else:
            # Generate new forecast
            return self.generate_forecast(product_id, days_ahead)

    def get_product_forecast_with_history(self, product_id: int, forecast_period: str = "1M") -> Dict:
        """Get forecast with historical data for enhanced visualization"""

        # Map forecast periods to days
        period_map = {"1M": 30, "3M": 90, "6M": 180, "1Y": 365}

        days_ahead = period_map.get(forecast_period, 30)

        logger.info(f"Forecast period: {forecast_period}, Days ahead: {days_ahead}")

        # Get historical sales data (past 1 year)
        historical_data = self._get_historical_sales_data_for_chart(product_id, days_back=365)

        # Generate fresh forecast with the specified period (don't use cached)
        forecast_result = self.generate_forecast(product_id, days_ahead)

        if not forecast_result.get("success"):
            return forecast_result

        return {
            **forecast_result,
            "historical_data": historical_data,
            "forecast_period": forecast_period,
            "forecast_period_days": days_ahead,
        }

    def _get_historical_sales_data(self, product_id: int) -> List[Dict]:
        """Retrieve historical sales data for a product"""
        sales_records = (
            self.db.query(SalesData).filter(SalesData.product_id == product_id).order_by(SalesData.date).all()
        )

        return [
            {"date": record.date, "quantity_sold": record.quantity_sold, "revenue": record.revenue}
            for record in sales_records
        ]

    def _get_historical_sales_data_for_chart(self, product_id: int, days_back: int = 365) -> List[Dict]:
        """Retrieve historical sales data for chart visualization"""
        cutoff_date = datetime.now() - timedelta(days=days_back)

        sales_records = (
            self.db.query(SalesData)
            .filter(SalesData.product_id == product_id, SalesData.date >= cutoff_date.date())
            .order_by(SalesData.date)
            .all()
        )

        return [
            {
                "date": record.date.isoformat(),
                "actual_sales": record.quantity_sold,
                "revenue": record.revenue,
                "type": "historical",
            }
            for record in sales_records
        ]

    def _prepare_prophet_data(self, sales_data: List[Dict]) -> pd.DataFrame:
        """Prepare sales data for Prophet model"""
        df = pd.DataFrame(sales_data)

        # Prophet requires 'ds' (date) and 'y' (value) columns
        df["ds"] = pd.to_datetime(df["date"])
        df["y"] = df["quantity_sold"]  # Forecast quantity sold

        # Remove any rows with missing values
        df = df.dropna(subset=["ds", "y"])

        # Ensure chronological order
        df = df.sort_values("ds").reset_index(drop=True)

        return df[["ds", "y"]]

    def _extract_forecast_data(self, forecast: pd.DataFrame, days_ahead: int) -> List[Dict]:
        """Extract forecast data for the prediction period"""
        # Get the last 'days_ahead' rows (future predictions)
        future_forecast = forecast.tail(days_ahead)

        forecast_data = []
        for _, row in future_forecast.iterrows():
            forecast_data.append(
                {
                    "date": row["ds"].strftime("%Y-%m-%d"),
                    "predicted_sales": max(0, round(row["yhat"], 2)),  # Ensure non-negative
                    "lower_bound": max(0, round(row["yhat_lower"], 2)),
                    "upper_bound": max(0, round(row["yhat_upper"], 2)),
                }
            )

        return forecast_data

    def _save_forecast_to_db(self, product_id: int, forecast_data: List[Dict], confidence_interval: float):
        """Save forecast data to database"""
        # Delete existing forecasts for this product
        self.db.query(ForecastData).filter(ForecastData.product_id == product_id).delete()

        # Save new forecast data
        for data_point in forecast_data:
            forecast_record = ForecastData(
                product_id=product_id,
                forecast_date=datetime.strptime(data_point["date"], "%Y-%m-%d"),
                predicted_sales=data_point["predicted_sales"],
                lower_bound=data_point["lower_bound"],
                upper_bound=data_point["upper_bound"],
                confidence_interval=confidence_interval,
            )
            self.db.add(forecast_record)

        self.db.commit()

    def _calculate_accuracy_metrics(self, df: pd.DataFrame, model: Prophet) -> Dict[str, float]:
        """Calculate forecast accuracy metrics using cross-validation"""
        try:
            from prophet.diagnostics import cross_validation, performance_metrics

            # Perform cross-validation
            df_cv = cross_validation(model, initial="30 days", period="7 days", horizon="7 days")

            # Calculate performance metrics
            df_p = performance_metrics(df_cv)

            return {
                "mae": float(df_p["mae"].mean()),  # Mean Absolute Error
                "mape": float(df_p["mape"].mean()),  # Mean Absolute Percentage Error
                "rmse": float(df_p["rmse"].mean()),  # Root Mean Square Error
            }
        except Exception as e:
            logger.warning(f"Could not calculate accuracy metrics: {str(e)}")
            return None

    def update_sales_data_from_orders(self, orders_data: List[Dict]):
        """Update sales data from order information"""
        try:
            for order in orders_data:
                order_date = datetime.fromisoformat(order["createdAt"].replace("Z", "+00:00"))

                # Process each line item in the order
                for line_item in order.get("lineItems", {}).get("edges", []):
                    item = line_item["node"]

                    # Extract product ID from variant
                    variant = item.get("variant", {})
                    if not variant or not variant.get("product", {}).get("id"):
                        continue

                    shopify_product_id = variant["product"]["id"]

                    # Find our internal product ID
                    product = self.db.query(Product).filter(Product.external_id == shopify_product_id).first()

                    if not product:
                        continue

                    quantity = int(item["quantity"])
                    revenue = float(item["originalTotalSet"]["shopMoney"]["amount"])

                    # Check if we already have sales data for this date and product
                    existing_sales = (
                        self.db.query(SalesData)
                        .filter(SalesData.product_id == product.id, SalesData.date == order_date.date())
                        .first()
                    )

                    if existing_sales:
                        # Update existing record
                        existing_sales.quantity_sold += quantity
                        existing_sales.revenue += revenue
                    else:
                        # Create new sales record
                        sales_record = SalesData(
                            product_id=product.id, date=order_date, quantity_sold=quantity, revenue=revenue
                        )
                        self.db.add(sales_record)

            self.db.commit()
            logger.info("Sales data updated successfully from orders")

        except Exception as e:
            logger.error(f"Error updating sales data from orders: {str(e)}")
            self.db.rollback()
            raise

    def populate_sales_data_from_existing_orders(self, generate_historical=True):
        """Populate sales data from existing orders in the database"""
        try:
            import json as _json
            import re
            import random

            # Clear existing sales data first
            self.db.query(SalesData).delete()
            self.db.commit()

            # Get all orders from the database
            orders = self.db.query(Order).all()
            logger.info(f"Processing {len(orders)} orders to populate sales data")

            # If we're generating historical data, create a date distribution
            historical_dates = []
            if generate_historical and orders:
                # Generate dates going back 60 days from the earliest order
                earliest_order_date = min(order.order_date for order in orders if order.order_date)
                base_date = earliest_order_date.date()

                # Create 60 days of historical dates
                for i in range(60):
                    historical_date = base_date - timedelta(days=i)
                    historical_dates.append(historical_date)

                logger.info(f"Generated {len(historical_dates)} historical dates for forecasting")

            for order in orders:
                try:
                    # Parse line items from JSON
                    line_items = _json.loads(order.line_items) if order.line_items else []

                    for item in line_items:
                        # Extract product information from line item
                        variant = item.get("variant") if item else None
                        product_obj = variant.get("product") if variant else None
                        product_gid = product_obj.get("id") if product_obj else None

                        if not product_gid:
                            continue

                        # Extract external ID from GraphQL ID
                        ext_id = None
                        match = re.search(r"/Product/(\d+)", product_gid)
                        if match:
                            ext_id = match.group(1)

                        if not ext_id:
                            continue

                        # Find our internal product
                        product = self.db.query(Product).filter(Product.external_id == ext_id).first()
                        if not product:
                            continue

                        # Get quantity and calculate revenue
                        quantity = int(item.get("quantity", 0))
                        if quantity <= 0:
                            continue

                        # Calculate revenue from price and quantity
                        price = float(variant.get("price", 0))
                        revenue = price * quantity

                        # Determine the date to use
                        if generate_historical and historical_dates:
                            # Distribute orders across historical dates
                            # Use a weighted distribution favoring more recent dates
                            weights = [1.0 / (i + 1) for i in range(len(historical_dates))]
                            order_date = random.choices(historical_dates, weights=weights)[0]
                        else:
                            # Use actual order date
                            if order.order_date:
                                order_date = order.order_date.date()
                            else:
                                logger.warning(f"Order {order.id} has no order_date, skipping")
                                continue

                        # Check if we already have sales data for this date and product
                        existing_sales = (
                            self.db.query(SalesData)
                            .filter(SalesData.product_id == product.id, SalesData.date == order_date)
                            .first()
                        )

                        if existing_sales:
                            # Update existing record
                            existing_sales.quantity_sold += quantity
                            existing_sales.revenue += revenue
                        else:
                            # Create new sales record
                            sales_record = SalesData(
                                product_id=product.id, date=order_date, quantity_sold=quantity, revenue=revenue
                            )
                            self.db.add(sales_record)

                except Exception as e:
                    logger.warning(f"Error processing order {order.id}: {str(e)}")
                    continue

            self.db.commit()

            # Log summary
            total_sales_records = self.db.query(SalesData).count()
            unique_dates = self.db.query(SalesData.date).distinct().count()
            logger.info(
                f"Sales data population complete. Total sales records: {total_sales_records}, Unique dates: {unique_dates}"
            )

            return {
                "success": True,
                "message": f"Successfully populated sales data from {len(orders)} orders across {unique_dates} dates",
                "total_sales_records": total_sales_records,
                "unique_dates": unique_dates,
                "historical_generated": generate_historical,
            }

        except Exception as e:
            logger.error(f"Error populating sales data from existing orders: {str(e)}")
            self.db.rollback()
            return {"success": False, "message": f"Failed to populate sales data: {str(e)}"}

    def _generate_single_model_forecast(
        self, df: pd.DataFrame, model_name: str, days_ahead: int, confidence_interval: float
    ) -> Optional[Dict[str, Any]]:
        """Generate forecast using a single model"""
        try:
            if model_name == "prophet":
                return self._forecast_with_prophet(df, days_ahead, confidence_interval)
            elif model_name.startswith("merlion_") and MERLION_AVAILABLE:
                return self._forecast_with_merlion(df, model_name, days_ahead)
            elif model_name.startswith("stats_") and STATSFORECAST_AVAILABLE:
                return self._forecast_with_statsforecast(df, model_name, days_ahead)
            elif model_name.startswith("skforecast_") and SKFORECAST_AVAILABLE:
                return self._forecast_with_skforecast(df, model_name, days_ahead)
            elif model_name.startswith("sktime_") and SKTIME_AVAILABLE:
                return self._forecast_with_sktime(df, model_name, days_ahead)
            elif model_name.startswith("statsmodels_") and STATSMODELS_AVAILABLE:
                return self._forecast_with_statsmodels(df, model_name, days_ahead)
            else:
                logger.warning(f"Unknown or unavailable model: {model_name}")
                return None
        except Exception as e:
            logger.error(f"Error forecasting with {model_name}: {str(e)}")
            return None

    def _forecast_with_prophet(self, df: pd.DataFrame, days_ahead: int, confidence_interval: float) -> Dict[str, Any]:
        """Generate forecast using Prophet"""
        model = Prophet(
            interval_width=confidence_interval,
            daily_seasonality=True,
            weekly_seasonality=True,
            yearly_seasonality=True if len(df) > 365 else False,
        )
        model.fit(df)

        future = model.make_future_dataframe(periods=days_ahead)
        forecast = model.predict(future)

        # Extract forecast data for the prediction period
        forecast_data = []
        for i in range(len(df), len(forecast)):
            forecast_data.append(
                {
                    "date": forecast.iloc[i]["ds"].strftime("%Y-%m-%d"),
                    "predicted_sales": max(0, forecast.iloc[i]["yhat"]),
                    "lower_bound": max(0, forecast.iloc[i]["yhat_lower"]),
                    "upper_bound": max(0, forecast.iloc[i]["yhat_upper"]),
                }
            )

        return {
            "model_name": "prophet",
            "forecast_data": forecast_data,
            "model_info": "Facebook Prophet - Time series forecasting with trend and seasonality",
        }

    def _forecast_with_merlion(self, df: pd.DataFrame, model_name: str, days_ahead: int) -> Dict[str, Any]:
        """Generate forecast using Merlion models"""
        if not MERLION_AVAILABLE:
            return None

        try:
            # Convert to Merlion TimeSeries format
            ts_data = [(row["ds"], row["y"]) for _, row in df.iterrows()]
            ts = TimeSeries.from_pd(pd.DataFrame(ts_data, columns=["timestamp", "value"]).set_index("timestamp"))

            # Select model based on name
            if model_name == "merlion_arima":
                model = Arima()
                model_info = "Merlion ARIMA - AutoRegressive Integrated Moving Average"
            elif model_name == "merlion_ets":
                model = ETS()
                model_info = "Merlion ETS - Error, Trend, Seasonality"
            elif model_name == "merlion_prophet":
                model = MerlionProphet()
                model_info = "Merlion Prophet - Facebook Prophet implementation"
            else:
                return None

            # Train and forecast
            model.train(ts)
            forecast, _ = model.forecast(days_ahead)

            # Convert forecast to our format
            forecast_data = []
            start_date = df["ds"].iloc[-1] + pd.Timedelta(days=1)

            for i, value in enumerate(forecast.values):
                forecast_date = start_date + pd.Timedelta(days=i)
                forecast_data.append(
                    {
                        "date": forecast_date.strftime("%Y-%m-%d"),
                        "predicted_sales": max(0, float(value[0])),
                        "lower_bound": max(0, float(value[0]) * 0.9),  # Simple confidence bounds
                        "upper_bound": max(0, float(value[0]) * 1.1),
                    }
                )

            return {"model_name": model_name, "forecast_data": forecast_data, "model_info": model_info}

        except Exception as e:
            logger.error(f"Error with Merlion {model_name}: {str(e)}")
            return None

    def _forecast_with_statsforecast(self, df: pd.DataFrame, model_name: str, days_ahead: int) -> Dict[str, Any]:
        """Generate forecast using StatsForecasting models"""
        if not STATSFORECAST_AVAILABLE:
            return None

        try:
            # Prepare data for StatsForecasting
            sf_df = df.copy()
            sf_df["unique_id"] = "product"
            sf_df = sf_df.rename(columns={"ds": "ds", "y": "y"})

            # Select model based on name
            if model_name == "stats_autoarima":
                models = [AutoARIMA()]
                model_info = "StatsForecasting AutoARIMA - Automatic ARIMA model selection"
            elif model_name == "stats_ets":
                models = [StatsETS()]
                model_info = "StatsForecasting ETS - Error, Trend, Seasonality"
            elif model_name == "stats_naive":
                models = [Naive()]
                model_info = "StatsForecasting Naive - Simple naive forecasting"
            else:
                return None

            # Create StatsForecast object and fit
            sf = StatsForecast(models=models, freq="D")
            forecasts = sf.forecast(df=sf_df, h=days_ahead)

            # Convert to our format
            forecast_data = []
            start_date = df["ds"].iloc[-1] + pd.Timedelta(days=1)

            for i in range(days_ahead):
                forecast_date = start_date + pd.Timedelta(days=i)
                pred_col = models[0].__class__.__name__
                predicted_value = max(0, float(forecasts.iloc[i][pred_col]))

                forecast_data.append(
                    {
                        "date": forecast_date.strftime("%Y-%m-%d"),
                        "predicted_sales": predicted_value,
                        "lower_bound": max(0, predicted_value * 0.9),
                        "upper_bound": max(0, predicted_value * 1.1),
                    }
                )

            return {"model_name": model_name, "forecast_data": forecast_data, "model_info": model_info}

        except Exception as e:
            logger.error(f"Error with StatsForecasting {model_name}: {str(e)}")
            return None

    def _forecast_with_skforecast(self, df: pd.DataFrame, model_name: str, days_ahead: int) -> Dict[str, Any]:
        """Generate forecast using SKForecast models"""
        if not SKFORECAST_AVAILABLE:
            return None

        try:
            # Prepare data for SKForecast
            y = df["y"].values

            # Select model based on name
            if model_name == "skforecast_rf":
                regressor = RandomForestRegressor(n_estimators=50, random_state=42)
                model_info = "SKForecast Random Forest - Ensemble tree-based forecasting"
            elif model_name == "skforecast_lr":
                regressor = LinearRegression()
                model_info = "SKForecast Linear Regression - Linear trend forecasting"
            else:
                return None

            # Create and fit forecaster
            forecaster = ForecasterRecursive(regressor=regressor, lags=min(7, len(y) // 2))
            forecaster.fit(y=y)

            # Generate forecast
            predictions = forecaster.predict(steps=days_ahead)

            # Convert to our format
            forecast_data = []
            start_date = df["ds"].iloc[-1] + pd.Timedelta(days=1)

            for i, pred in enumerate(predictions):
                forecast_date = start_date + pd.Timedelta(days=i)
                predicted_value = max(0, float(pred))

                forecast_data.append(
                    {
                        "date": forecast_date.strftime("%Y-%m-%d"),
                        "predicted_sales": predicted_value,
                        "lower_bound": max(0, predicted_value * 0.9),
                        "upper_bound": max(0, predicted_value * 1.1),
                    }
                )

            return {"model_name": model_name, "forecast_data": forecast_data, "model_info": model_info}

        except Exception as e:
            logger.error(f"Error with SKForecast {model_name}: {str(e)}")
            return None

    def _forecast_with_sktime(self, df: pd.DataFrame, model_name: str, days_ahead: int) -> Dict[str, Any]:
        """Generate forecast using SKTime models"""
        if not SKTIME_AVAILABLE:
            return None

        try:
            # Prepare data for SKTime
            y = pd.Series(df["y"].values, index=pd.date_range(start=df["ds"].iloc[0], periods=len(df), freq="D"))

            # Select model based on name
            if model_name == "sktime_arima":
                forecaster = SKTimeARIMA(order=(1, 1, 1), suppress_warnings=True)
                model_info = "SKTime ARIMA - AutoRegressive Integrated Moving Average"
            elif model_name == "sktime_exp_smoothing":
                forecaster = ExponentialSmoothing(trend="add", seasonal="add", sp=7)
                model_info = "SKTime Exponential Smoothing - Trend and seasonal smoothing"
            else:
                return None

            # Fit and forecast
            forecaster.fit(y)
            fh = list(range(1, days_ahead + 1))
            predictions = forecaster.predict(fh)

            # Convert to our format
            forecast_data = []
            start_date = df["ds"].iloc[-1] + pd.Timedelta(days=1)

            for i, pred in enumerate(predictions):
                forecast_date = start_date + pd.Timedelta(days=i)
                predicted_value = max(0, float(pred))

                forecast_data.append(
                    {
                        "date": forecast_date.strftime("%Y-%m-%d"),
                        "predicted_sales": predicted_value,
                        "lower_bound": max(0, predicted_value * 0.9),
                        "upper_bound": max(0, predicted_value * 1.1),
                    }
                )

            return {"model_name": model_name, "forecast_data": forecast_data, "model_info": model_info}

        except Exception as e:
            logger.error(f"Error with SKTime {model_name}: {str(e)}")
            return None

    def _forecast_with_statsmodels(self, df: pd.DataFrame, model_name: str, days_ahead: int) -> Dict[str, Any]:
        """Generate forecast using StatsModels"""
        if not STATSMODELS_AVAILABLE:
            return None

        try:
            # Prepare data
            y = df["y"].values

            # Select model based on name
            if model_name == "statsmodels_arima":
                model = StatsARIMA(y, order=(1, 1, 1))
                model_info = "StatsModels ARIMA - AutoRegressive Integrated Moving Average"
            elif model_name == "statsmodels_exp_smoothing":
                model = StatsExpSmoothing(y, trend="add", seasonal="add", seasonal_periods=7)
                model_info = "StatsModels Exponential Smoothing - Holt-Winters method"
            else:
                return None

            # Fit and forecast
            fitted_model = model.fit()
            predictions = fitted_model.forecast(steps=days_ahead)

            # Convert to our format
            forecast_data = []
            start_date = df["ds"].iloc[-1] + pd.Timedelta(days=1)

            for i, pred in enumerate(predictions):
                forecast_date = start_date + pd.Timedelta(days=i)
                predicted_value = max(0, float(pred))

                forecast_data.append(
                    {
                        "date": forecast_date.strftime("%Y-%m-%d"),
                        "predicted_sales": predicted_value,
                        "lower_bound": max(0, predicted_value * 0.9),
                        "upper_bound": max(0, predicted_value * 1.1),
                    }
                )

            return {"model_name": model_name, "forecast_data": forecast_data, "model_info": model_info}

        except Exception as e:
            logger.error(f"Error with StatsModels {model_name}: {str(e)}")
            return None

    def _detect_anomalies(self, df: pd.DataFrame) -> List[Dict[str, Any]]:
        """Detect anomalies in historical data using ADTK"""
        if not ADTK_AVAILABLE:
            return []

        try:
            # Prepare data for ADTK
            ts = pd.Series(df["y"].values, index=pd.to_datetime(df["ds"]))
            ts = validate_series(ts)

            # Use multiple anomaly detectors
            detectors = [
                ("threshold", ThresholdAD(high=ts.quantile(0.95), low=ts.quantile(0.05))),
                ("quantile", QuantileAD(high=0.95, low=0.05)),
                ("iqr", InterQuartileRangeAD(c=1.5)),
            ]

            anomalies = []
            for detector_name, detector in detectors:
                try:
                    anomaly_scores = detector.fit_detect(ts)
                    anomaly_dates = anomaly_scores[anomaly_scores].index

                    for date in anomaly_dates:
                        anomalies.append(
                            {
                                "date": date.strftime("%Y-%m-%d"),
                                "value": float(ts[date]),
                                "detector": detector_name,
                                "type": "anomaly",
                            }
                        )
                except Exception as e:
                    logger.warning(f"Error with {detector_name} detector: {str(e)}")
                    continue

            return anomalies

        except Exception as e:
            logger.error(f"Error detecting anomalies: {str(e)}")
            return []

    def _calculate_ensemble_forecast(self, forecasts: Dict[str, Dict], days_ahead: int) -> List[Dict[str, Any]]:
        """Calculate ensemble forecast by averaging all model predictions"""
        if not forecasts:
            return []

        try:
            ensemble_data = []

            for day in range(days_ahead):
                predictions = []
                lower_bounds = []
                upper_bounds = []
                date_str = None

                for model_name, forecast_result in forecasts.items():
                    forecast_data = forecast_result.get("forecast_data", [])
                    if day < len(forecast_data):
                        point = forecast_data[day]
                        predictions.append(point["predicted_sales"])
                        lower_bounds.append(point["lower_bound"])
                        upper_bounds.append(point["upper_bound"])
                        if date_str is None:
                            date_str = point["date"]

                if predictions and date_str:
                    ensemble_data.append(
                        {
                            "date": date_str,
                            "predicted_sales": np.mean(predictions),
                            "lower_bound": np.mean(lower_bounds),
                            "upper_bound": np.mean(upper_bounds),
                            "model_count": len(predictions),
                        }
                    )

            return ensemble_data

        except Exception as e:
            logger.error(f"Error calculating ensemble forecast: {str(e)}")
            return []
