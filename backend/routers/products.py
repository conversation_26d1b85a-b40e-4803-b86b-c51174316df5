from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session
from typing import List, Optional, Dict, Any
from pydantic import BaseModel
import json
import re

from database import get_db
from models import User, Store, Product, Order  # Add Order import
from routers.auth import get_current_user
from services.forecasting_service import SalesForecastingService

router = APIRouter()


class ProductResponse(BaseModel):
    id: int
    external_id: str
    title: str
    description: Optional[str] = None
    description_html: Optional[str] = None
    handle: Optional[str] = None
    price: float
    compare_at_price: Optional[float] = None
    inventory_quantity: int
    sku: Optional[str] = None
    barcode: Optional[str] = None
    weight: Optional[float] = None
    weight_unit: Optional[str] = None
    status: str
    product_type: Optional[str] = None
    vendor: Optional[str] = None
    tags: Optional[List[str]] = None
    images: Optional[List[str]] = None
    variants: Optional[List[Dict]] = None
    store_id: int
    order_count: Optional[int] = 0  # Add order_count field

    class Config:
        from_attributes = True

    @classmethod
    def from_orm(cls, obj, order_count=0):
        # Parse JSON fields
        tags = json.loads(obj.tags) if obj.tags else []
        images = json.loads(obj.images) if obj.images else []
        variants = json.loads(obj.variants) if obj.variants else []

        return cls(
            id=obj.id,
            external_id=obj.external_id,
            title=obj.title,
            description=obj.description,
            description_html=obj.description_html,
            handle=obj.handle,
            price=obj.price,
            compare_at_price=obj.compare_at_price,
            inventory_quantity=obj.inventory_quantity,
            sku=obj.sku,
            barcode=obj.barcode,
            weight=obj.weight,
            weight_unit=obj.weight_unit,
            status=obj.status,
            product_type=obj.product_type,
            vendor=obj.vendor,
            tags=tags,
            images=images,
            variants=variants,
            store_id=obj.store_id,
            order_count=order_count,
        )


@router.get("/", response_model=List[ProductResponse])
async def get_products(
    store_id: Optional[int] = None, current_user: User = Depends(get_current_user), db: Session = Depends(get_db)
):
    query = db.query(Product).join(Store).filter(Store.owner_id == current_user.id)

    if store_id:
        query = query.filter(Product.store_id == store_id)

    products = query.all()

    # Build a map from external_id to product.id
    external_id_to_id = {str(p.external_id).strip(): p.id for p in products}
    order_count_map = {p.id: 0 for p in products}

    orders = db.query(Order).filter(Order.store_id.in_([p.store_id for p in products])).all()
    import json as _json

    for order in orders:
        try:
            line_items = _json.loads(order.line_items) if order.line_items else []
            products_in_order = set()
            for item in line_items:
                # Defensive: skip if item or variant or product is None
                variant = item.get("variant") if item else None
                product_obj = variant.get("product") if variant else None
                product_gid = product_obj.get("id") if product_obj else None
                ext_id = None
                if product_gid:
                    match = re.search(r"/Product/(\d+)", product_gid)
                    if match:
                        ext_id = match.group(1)
                if ext_id and ext_id in external_id_to_id:
                    products_in_order.add(external_id_to_id[ext_id])
            for pid in products_in_order:
                order_count_map[pid] += 1
        except Exception as e:
            print(f"Error parsing order {order.id}: {e}")
            continue

    return [ProductResponse.from_orm(product, order_count=order_count_map.get(product.id, 0)) for product in products]


@router.get("/{product_id}", response_model=ProductResponse)
async def get_product(product_id: int, current_user: User = Depends(get_current_user), db: Session = Depends(get_db)):
    product = db.query(Product).join(Store).filter(Product.id == product_id, Store.owner_id == current_user.id).first()

    if not product:
        raise HTTPException(status_code=404, detail="Product not found")

    return ProductResponse.from_orm(product)


@router.get("/{product_id}/forecast")
async def get_product_forecast(
    product_id: int,
    forecast_period: str = "1M",
    models: Optional[str] = None,  # Comma-separated list of models
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db),
):
    """Get multi-model sales forecast for a specific product with historical data and anomaly detection"""
    # Verify product ownership
    product = db.query(Product).join(Store).filter(Product.id == product_id, Store.owner_id == current_user.id).first()

    if not product:
        raise HTTPException(status_code=404, detail="Product not found")

    # Validate forecast period
    valid_periods = ["1M", "3M", "6M", "1Y"]
    if forecast_period not in valid_periods:
        raise HTTPException(status_code=400, detail=f"Invalid forecast period. Must be one of: {valid_periods}")

    # Parse models parameter
    selected_models = None
    if models:
        selected_models = [model.strip() for model in models.split(",")]

    # Log the forecast period for debugging
    import logging

    logger = logging.getLogger(__name__)
    logger.info(f"Generating multi-model forecast for product {product_id} with period {forecast_period}")

    # Map forecast periods to days
    period_map = {"1M": 30, "3M": 90, "6M": 180, "1Y": 365}
    days_ahead = period_map.get(forecast_period, 30)

    # Generate multi-model forecast
    forecasting_service = SalesForecastingService(db)

    # Get historical data for chart
    historical_data = forecasting_service._get_historical_sales_data_for_chart(product_id, days_back=365)

    # Generate multi-model forecast
    forecast_result = forecasting_service.generate_multi_model_forecast(product_id, days_ahead, models=selected_models)

    if not forecast_result.get("success"):
        return forecast_result

    # Add historical data and product info to the result
    forecast_result["historical_data"] = historical_data
    forecast_result["product_info"] = {
        "id": product.id,
        "title": product.title,
        "sku": product.sku,
        "current_inventory": product.inventory_quantity,
    }
    forecast_result["forecast_period"] = forecast_period

    return forecast_result


@router.get("/forecast/models")
async def get_available_forecast_models(
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db),
):
    """Get list of available forecasting models"""
    forecasting_service = SalesForecastingService(db)
    return {
        "success": True,
        "available_models": forecasting_service.available_models,
        "model_descriptions": {
            "prophet": "Facebook Prophet - Time series forecasting with trend and seasonality",
            "merlion_arima": "Merlion ARIMA - AutoRegressive Integrated Moving Average",
            "merlion_ets": "Merlion ETS - Error, Trend, Seasonality",
            "merlion_prophet": "Merlion Prophet - Facebook Prophet implementation",
            "stats_autoarima": "StatsForecasting AutoARIMA - Automatic ARIMA model selection",
            "stats_ets": "StatsForecasting ETS - Error, Trend, Seasonality",
            "stats_naive": "StatsForecasting Naive - Simple naive forecasting",
            "darts_arima": "Darts ARIMA - AutoRegressive Integrated Moving Average",
            "darts_exp_smoothing": "Darts Exponential Smoothing - Trend and seasonal smoothing",
            "darts_linear_regression": "Darts Linear Regression - Linear trend forecasting",
            "darts_random_forest": "Darts Random Forest - Ensemble tree-based forecasting",
            "sktime_exp_smoothing": "SKTime Exponential Smoothing - Trend and seasonal smoothing",
            "sktime_trend": "SKTime Trend Forecaster - Linear trend forecasting",
            "statsmodels_arima": "StatsModels ARIMA - AutoRegressive Integrated Moving Average",
            "statsmodels_exp_smoothing": "StatsModels Exponential Smoothing - Holt-Winters method",
        },
    }


@router.post("/{product_id}/forecast/generate")
async def generate_product_forecast(
    product_id: int,
    days_ahead: int = 30,
    confidence_interval: float = 0.95,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db),
):
    """Force generate a new forecast for a product"""
    # Verify product ownership
    product = db.query(Product).join(Store).filter(Product.id == product_id, Store.owner_id == current_user.id).first()

    if not product:
        raise HTTPException(status_code=404, detail="Product not found")

    # Generate new forecast
    forecasting_service = SalesForecastingService(db)
    forecast_result = forecasting_service.generate_forecast(product_id, days_ahead, confidence_interval)

    return forecast_result


@router.post("/populate-sales-data")
async def populate_sales_data(
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db),
):
    """Populate sales data from existing orders for forecasting"""
    # Initialize forecasting service
    forecasting_service = SalesForecastingService(db)

    # Populate sales data from existing orders
    result = forecasting_service.populate_sales_data_from_existing_orders()

    return result
