import React, { useRef } from "react";
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Tooltip,
  Legend,
  Filler,
} from "chart.js";
import type { ChartOptions, ChartData } from "chart.js";
import { Line } from "react-chartjs-2";
import {
  TrendingUp,
  Calendar,
  Target,
  AlertCircle,
  BarChart3,
} from "lucide-react";

ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Tooltip,
  Legend,
  Filler
);

interface ForecastData {
  date: string;
  predicted_sales: number;
  lower_bound: number;
  upper_bound: number;
}

interface ProductInfo {
  id: number;
  title: string;
  sku?: string;
  current_inventory: number;
}

interface HistoricalData {
  date: string;
  actual_sales: number;
  revenue: number;
  type: string;
}

interface SalesForecastChartProps {
  forecastData: ForecastData[];
  historicalData?: HistoricalData[];
  productInfo: ProductInfo;
  isLoading?: boolean;
  error?: string;
  onRefresh?: () => void;
  onPopulateSalesData?: () => void;
  forecastPeriod?: string;
}

const SalesForecastChart: React.FC<SalesForecastChartProps> = ({
  forecastData,
  historicalData = [],
  productInfo,
  isLoading = false,
  error,
  onRefresh,
  onPopulateSalesData,
  forecastPeriod = "1M",
}) => {
  const chartRef = useRef<ChartJS<"line">>(null);

  // Combine historical and forecast data for chart
  const allDates: string[] = [];
  const historicalSales: (number | null)[] = [];
  const predictedSales: (number | null)[] = [];
  const upperBounds: (number | null)[] = [];
  const lowerBounds: (number | null)[] = [];

  // Add historical data
  historicalData.forEach((item) => {
    const date = new Date(item.date);
    const formattedDate = date.toLocaleDateString("en-US", {
      month: "short",
      day: "numeric",
    });
    allDates.push(formattedDate);
    historicalSales.push(item.actual_sales);
    predictedSales.push(null);
    upperBounds.push(null);
    lowerBounds.push(null);
  });

  // Add forecast data
  forecastData.forEach((item) => {
    const date = new Date(item.date);
    const formattedDate = date.toLocaleDateString("en-US", {
      month: "short",
      day: "numeric",
    });
    allDates.push(formattedDate);
    historicalSales.push(null);
    predictedSales.push(item.predicted_sales);
    upperBounds.push(item.upper_bound);
    lowerBounds.push(item.lower_bound);
  });

  // Prepare chart data
  const chartData: ChartData<"line"> = {
    labels: allDates,
    datasets: [
      {
        label: "Historical Sales",
        data: historicalSales,
        borderColor: "rgb(107, 114, 128)",
        backgroundColor: "rgba(107, 114, 128, 0.1)",
        borderWidth: 2,
        fill: false,
        tension: 0.4,
        pointRadius: 2,
        pointHoverRadius: 6,
        pointBackgroundColor: "rgb(107, 114, 128)",
        pointBorderColor: "#ffffff",
        pointBorderWidth: 1,
      },
      {
        label: "Predicted Sales",
        data: predictedSales,
        borderColor: "rgb(59, 130, 246)",
        backgroundColor: "rgba(59, 130, 246, 0.1)",
        borderWidth: 3,
        fill: false,
        tension: 0.4,
        pointRadius: 4,
        pointHoverRadius: 8,
        pointBackgroundColor: "rgb(59, 130, 246)",
        pointBorderColor: "#ffffff",
        pointBorderWidth: 2,
      },
      {
        label: "Upper Bound",
        data: upperBounds,
        borderColor: "rgba(34, 197, 94, 0.3)",
        backgroundColor: "rgba(34, 197, 94, 0.1)",
        borderWidth: 1,
        fill: 3, // Fill to the next dataset (Lower Bound)
        tension: 0.4,
        pointRadius: 0,
        pointHoverRadius: 0,
      },
      {
        label: "Lower Bound",
        data: lowerBounds,
        borderColor: "rgba(34, 197, 94, 0.3)",
        backgroundColor: "rgba(34, 197, 94, 0.1)",
        borderWidth: 1,
        fill: false,
        tension: 0.4,
        pointRadius: 0,
        pointHoverRadius: 0,
      },
    ],
  };

  // Chart options for interactivity
  const options: ChartOptions<"line"> = {
    responsive: true,
    maintainAspectRatio: false,
    interaction: {
      mode: "index",
      intersect: false,
    },
    plugins: {
      title: {
        display: true,
        text: `Sales Forecast - ${productInfo.title}`,
        font: {
          size: 18,
          weight: "bold",
        },
        color: "#1f2937",
        padding: 20,
      },
      legend: {
        display: true,
        position: "top",
        labels: {
          usePointStyle: true,
          padding: 20,
          font: {
            size: 12,
          },
          filter: (legendItem) => {
            // Hide the bounds from legend, only show main prediction
            return legendItem.text === "Predicted Sales";
          },
        },
      },
      tooltip: {
        mode: "index",
        intersect: false,
        backgroundColor: "rgba(0, 0, 0, 0.8)",
        titleColor: "#ffffff",
        bodyColor: "#ffffff",
        borderColor: "rgba(59, 130, 246, 0.5)",
        borderWidth: 1,
        cornerRadius: 8,
        padding: 12,
        callbacks: {
          title: (context) => {
            const index = context[0].dataIndex;
            const date = new Date(forecastData[index].date);
            return date.toLocaleDateString("en-US", {
              weekday: "long",
              year: "numeric",
              month: "long",
              day: "numeric",
            });
          },
          label: (context) => {
            const label = context.dataset.label || "";
            const value = context.parsed.y;

            if (label === "Predicted Sales") {
              return `${label}: ${value.toFixed(1)} units`;
            } else if (label === "Upper Bound" || label === "Lower Bound") {
              const index = context.dataIndex;
              const lowerBound = forecastData[index].lower_bound;
              const upperBound = forecastData[index].upper_bound;
              return `Confidence Range: ${lowerBound.toFixed(
                1
              )} - ${upperBound.toFixed(1)} units`;
            }
            return "";
          },
        },
      },
    },
    scales: {
      x: {
        display: true,
        title: {
          display: true,
          text: "Date",
          font: {
            size: 14,
            weight: "bold",
          },
          color: "#374151",
        },
        grid: {
          color: "rgba(156, 163, 175, 0.2)",
        },
        ticks: {
          color: "#6b7280",
          font: {
            size: 11,
          },
        },
      },
      y: {
        display: true,
        title: {
          display: true,
          text: "Predicted Sales (Units)",
          font: {
            size: 14,
            weight: "bold",
          },
          color: "#374151",
        },
        beginAtZero: true,
        grid: {
          color: "rgba(156, 163, 175, 0.2)",
        },
        ticks: {
          color: "#6b7280",
          font: {
            size: 11,
          },
          callback: function (value) {
            return Math.round(Number(value));
          },
        },
      },
    },
    elements: {
      point: {
        radius: 4,
        hoverRadius: 8,
      },
      line: {
        borderJoinStyle: "round",
      },
    },
    animation: {
      duration: 1000,
      easing: "easeInOutQuart",
    },
  };

  // Calculate summary statistics
  const totalPredictedSales = forecastData.reduce(
    (sum, item) => sum + item.predicted_sales,
    0
  );
  const avgDailySales =
    forecastData.length > 0 ? totalPredictedSales / forecastData.length : 0;
  const maxSalesDay =
    forecastData.length > 0
      ? forecastData.reduce((max, item) =>
          item.predicted_sales > max.predicted_sales ? item : max
        )
      : { predicted_sales: 0, date: new Date().toISOString() };

  // Calculate historical statistics
  const totalHistoricalSales = historicalData.reduce(
    (sum, item) => sum + item.actual_sales,
    0
  );
  const avgHistoricalSales =
    historicalData.length > 0
      ? totalHistoricalSales / historicalData.length
      : 0;

  if (error) {
    const isInsufficientDataError = error.includes(
      "minimum 10 data points required"
    );

    return (
      <div className="bg-white rounded-lg shadow-md p-6">
        <div className="flex items-center justify-center text-red-500 mb-4">
          <AlertCircle size={48} />
        </div>
        <h3 className="text-lg font-semibold text-center mb-2">
          Forecast Error
        </h3>
        <p className="text-gray-600 text-center mb-4">{error}</p>

        {isInsufficientDataError && (
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-4">
            <h4 className="font-medium text-blue-900 mb-2">💡 Quick Fix</h4>
            <p className="text-blue-800 text-sm mb-3">
              This error occurs when there isn't enough historical sales data.
              You can populate sales data from your existing orders to enable
              forecasting.
            </p>
            {onPopulateSalesData && (
              <button
                onClick={onPopulateSalesData}
                className="w-full bg-blue-500 text-white py-2 px-4 rounded-lg hover:bg-blue-600 transition-colors mb-2"
              >
                Populate Sales Data from Orders
              </button>
            )}
          </div>
        )}

        {onRefresh && (
          <button
            onClick={onRefresh}
            className="w-full bg-gray-500 text-white py-2 px-4 rounded-lg hover:bg-gray-600 transition-colors"
          >
            Try Again
          </button>
        )}
      </div>
    );
  }

  if (isLoading) {
    return (
      <div className="bg-white rounded-lg shadow-md p-6">
        <div className="animate-pulse">
          <div className="h-6 bg-gray-200 rounded mb-4"></div>
          <div className="h-64 bg-gray-200 rounded mb-4"></div>
          <div className="grid grid-cols-3 gap-4">
            <div className="h-16 bg-gray-200 rounded"></div>
            <div className="h-16 bg-gray-200 rounded"></div>
            <div className="h-16 bg-gray-200 rounded"></div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white rounded-lg shadow-md p-6">
      {/* Interactive Chart */}
      <div className="h-80 mb-6">
        <Line ref={chartRef} data={chartData} options={options} />
      </div>

      {/* Summary Statistics */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-4">
        {historicalData.length > 0 && (
          <div className="bg-gray-50 rounded-lg p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600 font-medium">
                  Historical Avg
                </p>
                <p className="text-2xl font-bold text-gray-900">
                  {avgHistoricalSales.toFixed(1)} units
                </p>
                <p className="text-xs text-gray-500">
                  Past {historicalData.length} days
                </p>
              </div>
              <BarChart3 className="text-gray-500" size={24} />
            </div>
          </div>
        )}

        <div className="bg-blue-50 rounded-lg p-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-blue-600 font-medium">
                Total Forecast ({forecastPeriod})
              </p>
              <p className="text-2xl font-bold text-blue-900">
                {totalPredictedSales.toFixed(0)} units
              </p>
            </div>
            <TrendingUp className="text-blue-500" size={24} />
          </div>
        </div>

        <div className="bg-green-50 rounded-lg p-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-green-600 font-medium">
                Daily Average
              </p>
              <p className="text-2xl font-bold text-green-900">
                {avgDailySales.toFixed(1)} units
              </p>
            </div>
            <Target className="text-green-500" size={24} />
          </div>
        </div>

        <div className="bg-purple-50 rounded-lg p-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-purple-600 font-medium">Peak Day</p>
              <p className="text-lg font-bold text-purple-900">
                {maxSalesDay.predicted_sales.toFixed(0)} units
              </p>
              <p className="text-xs text-purple-600">
                {new Date(maxSalesDay.date).toLocaleDateString()}
              </p>
            </div>
            <Calendar className="text-purple-500" size={24} />
          </div>
        </div>
      </div>

      {/* Product Info */}
      <div className="bg-gray-50 rounded-lg p-4">
        <h4 className="font-semibold text-gray-900 mb-2">
          Product Information
        </h4>
        <div className="grid grid-cols-2 gap-4 text-sm">
          <div>
            <span className="text-gray-600">SKU:</span>
            <span className="ml-2 font-medium">{productInfo.sku || "N/A"}</span>
          </div>
          <div>
            <span className="text-gray-600">Current Inventory:</span>
            <span className="ml-2 font-medium">
              {productInfo.current_inventory} units
            </span>
          </div>
        </div>
      </div>

      {/* Refresh Button */}
      {onRefresh && (
        <div className="mt-4 text-center">
          <button
            onClick={onRefresh}
            className="bg-gray-100 text-gray-700 py-2 px-4 rounded-lg hover:bg-gray-200 transition-colors text-sm"
          >
            Refresh Forecast
          </button>
        </div>
      )}
    </div>
  );
};

export default SalesForecastChart;
